import "./App.css";

import { useState } from "react";
import Counter from "./components/Counter";

function App() {
  const [count, setCount] = useState(0);

  return (
    <div style={{ padding: 20 }}>
      <h1>父组件 Count: {count}</h1>
      <button onClick={() => setCount(count + 1)}>父组件 +1</button>

      <hr />

      {/* 根节点没有父组件，所以 parentCount 不传 */}
      <Counter />
    </div>
  );
}

export default App;
